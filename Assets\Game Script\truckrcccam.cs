using UnityEngine;

public class truckrcccam : MonoBehaviour
{
    public RCC_Camera cam;
    public GameObject Truck;
    public float rcccamhight;
    public float rcccamdistance;

    void Update()
    {
        if (Truck != null && Truck.GetComponent<RCC_CarControllerV4>().enabled == true)
        {
            cam.TPSHeight = rcccamhight;
            cam.TPSDistance = rcccamdistance;
        }
        else if (Truck != null && Truck.GetComponent<RCC_CarControllerV4>().enabled == false)
        {
            cam.TPSHeight = 13f;
            cam.TPSDistance = 4.7f;
        }
    }
}
